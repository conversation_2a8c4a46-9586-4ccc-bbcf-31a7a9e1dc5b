<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="9" failures="0" errors="0" time="1.527">
  <testsuite name="AuthController" errors="0" failures="0" skipped="0" timestamp="2025-08-12T06:13:48" time="1.418" tests="9">
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should return 200 and auth token for valid credentials" time="0.133">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › successful login" name="should handle email case normalization" time="0.076">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing email" time="0.058">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for missing password" time="0.064">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › validation errors" name="should return 400 for invalid email format" time="0.046">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 401 for non-existent user" time="0.043">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › authentication errors" name="should return 401 for invalid password" time="0.048">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle empty request body" time="0.042">
    </testcase>
    <testcase classname="AuthController › POST /api/v1/auth/login › edge cases" name="should handle malformed JSON" time="0.02">
    </testcase>
  </testsuite>
</testsuites>